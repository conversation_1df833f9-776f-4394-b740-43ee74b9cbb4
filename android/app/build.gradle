plugins {
    id "com.android.application"
    id "org.jetbrains.kotlin.android"
    id "com.google.gms.google-services"
    id "com.google.firebase.crashlytics"
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}


android {
    namespace "com.chinloyal.wordle"
    compileSdkVersion 35
    buildToolsVersion "34.0.0"
    ndkVersion "27.2.12479018"

    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility JavaVersion.VERSION_21
        targetCompatibility JavaVersion.VERSION_21
    }

    kotlinOptions {
        jvmTarget = '21'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    packagingOptions {
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INF/ASL2.0'
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/NOTICE'
    }


    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.chinloyal.wordle"
        minSdkVersion 23
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"
        multiDexEnabled true
        ndk {
            abiFilters "arm64-v8a", "armeabi-v7a", "x86_64"
        }

    }

    // if (System.getenv("ANDROID_KEYSTORE_FILE")) {
    signingConfigs {
        release {
            storeFile rootProject.file('key.keystore')
            storePassword "w0rld!"
            keyAlias "wordle"
            keyPassword "w0rld!"
        }
    }
    // }

    buildTypes {
        debug {
            signingConfig signingConfigs.debug
            firebaseCrashlytics {
                mappingFileUploadEnabled false
            }
        }

        release {

            // if (System.getenv("ANDROID_KEYSTORE_FILE")) {
            minifyEnabled true
            signingConfig signingConfigs.release
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            debuggable false
            firebaseCrashlytics {
                mappingFileUploadEnabled true
            }
            // } else {
            //     signingConfig signingConfigs.debug

            // }
        }
    }

    flavorDimensions "app"
    productFlavors {
        prod {
            dimension "app"
        }

        staging {
            dimension "app"
            applicationIdSuffix ".staging"
            versionNameSuffix "-staging"
        }
    }

    lintOptions {
        checkReleaseBuilds false
    }
}

flutter {
    source '../..'
}
repositories {
    google()
    mavenCentral()
    maven { url 'https://android-sdk.is.com/' }
    maven { url "https://dl-maven-android.mintegral.com/repository/mbridge_android_sdk_oversea" }
    maven { url 'https://maven.singular.net/' }

}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation "androidx.multidex:multidex:2.0.1"
    implementation 'com.google.ads.mediation:facebook:6.16.0.0'
    implementation 'com.google.ads.mediation:pangle:5.7.0.1.0'
    implementation 'com.google.ads.mediation:vungle:7.1.0.0'
    implementation "androidx.core:core-splashscreen:1.0.0"
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.4'

// android sdk
    implementation 'com.ironsource.sdk:mediationsdk:8.1.0'
    implementation 'com.ironsource:adqualitysdk:7.20.0'

    implementation 'com.google.android.gms:play-services-appset:16.0.0'
    implementation 'com.google.android.gms:play-services-ads-identifier:18.0.1'
    implementation 'com.google.android.gms:play-services-basement:18.1.0'

// Add Applovin Network
    implementation 'com.ironsource.adapters:applovinadapter:4.3.42'
    implementation 'com.applovin:applovin-sdk:12.3.1'
// Add Facebook Network
    implementation 'com.ironsource.adapters:facebookadapter:4.3.45'
    implementation 'com.facebook.android:audience-network-sdk:6.16.0'

//overseas market
    implementation 'com.ironsource.adapters:mintegraladapter:4.3.24'
    implementation 'com.mbridge.msdk.oversea:mbbid:16.6.31'
    implementation 'com.mbridge.msdk.oversea:reward:16.6.31'
    implementation 'com.mbridge.msdk.oversea:mbbanner:16.6.31'
    implementation 'com.mbridge.msdk.oversea:newinterstitial:16.6.31'
// Add UnityAds Network
    implementation 'com.ironsource.adapters:unityadsadapter:4.3.37'
    implementation 'com.unity3d.ads:unity-ads:4.10.0'

    //Digital turbine
    implementation 'com.ironsource.adapters:fyberadapter:4.3.30'
    implementation "com.fyber:marketplace-sdk:8.2.6"

    // Singular
    implementation 'com.singular.sdk:singular_sdk:12.5.5'

    // firebase analytics
    implementation(platform("com.google.firebase:firebase-bom:33.1.0"))
    implementation("com.google.firebase:firebase-analytics")
}