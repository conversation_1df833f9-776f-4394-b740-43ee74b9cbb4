                        -H/Users/<USER>/flutter/packages/flutter_tools/gradle/src/main/groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-D<PERSON>DROID_PLATFORM=android-23
-DANDROID_ABI=x86_64
-DCMAKE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.2.12479018
-DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.2.12479018
-DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/27.2.12479018/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Desktop/NicheProjects/worde/build/app/intermediates/cxx/RelWithDebInfo/6h3h5za6/obj/x86_64
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Desktop/NicheProjects/worde/build/app/intermediates/cxx/RelWithDebInfo/6h3h5za6/obj/x86_64
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-B/Users/<USER>/Desktop/NicheProjects/worde/android/app/.cxx/RelWithDebInfo/6h3h5za6/x86_64
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2