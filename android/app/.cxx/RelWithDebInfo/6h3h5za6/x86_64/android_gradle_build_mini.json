{"buildFiles": ["/Users/<USER>/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Desktop/NicheProjects/worde/android/app/.cxx/RelWithDebInfo/6h3h5za6/x86_64", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Desktop/NicheProjects/worde/android/app/.cxx/RelWithDebInfo/6h3h5za6/x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}