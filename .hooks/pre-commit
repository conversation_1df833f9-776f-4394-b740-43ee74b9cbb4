#!/bin/bash

. "./.hooks/utils/log.sh"

i "Linting project...";

.fvm/flutter_sdk/bin/dart format --set-exit-if-changed . || fvm dart format --set-exit-if-changed . || dart format --set-exit-if-changed .

if [[ $? != 0 ]]; then
    warn "Your files have been reformatted, please re-stage and commit again."
    exit 1
fi


.fvm/flutter_sdk/bin/dart analyze || fvm dart analyze || flutter analyze

if [[ $? != 0 ]]; then
    error "Linting failed, Please fix the issues listed above to commit."
    exit 1
fi

.fvm/flutter_sdk/bin/flutter test || fvm flutter test || flutter test

if [[ $? != 0 ]]; then
    error "Make sure your tests are passing before committing."
    exit 1
fi