import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_overlay_loader/flutter_overlay_loader.dart';
import 'package:flutter_svg/svg.dart';
import 'package:share_plus/share_plus.dart';
import 'package:wordle/main.dart';

import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_images.dart';
import '../../../core/constants/events.dart';
import '../../../core/constants/font_family.dart';
import '../../../core/helpers/images.dart';
import '../../../core/helpers/toast.dart';
import '../../../core/utils/env.dart';
import '../../../core/utils/service_locator.dart';
import '../../../domain/auth/store/auth_store.dart';
import '../../../domain/main/stores/main_store.dart';
import '../../../router/app_router.dart';
import '../../../services/auth/auth_service.dart';
import '../../../services/events/event_logger_contract.dart';
import '../../views/settings/settings_view.dart';
import '../../views/shop/shop_view.dart';
import '../app_avatar.dart';
import '../coins_display.dart';
import '../how_to_modal.dart';

class CustomDrawer extends StatelessWidget {
  CustomDrawer({
    Key? key,
  }) : super(key: key);

  final _authStore = ServiceLocator.locate<AuthStore>();
  final _eventLogger = ServiceLocator.locate<EventLoggerContract>();
  final mainStore = ServiceLocator.locate<MainStore>();
  final _authService = ServiceLocator.locate<AuthService>();

  @override
  Widget build(BuildContext context) {
    return Drawer(
      backgroundColor: AppColors.black,
      child: ListView(
        padding: EdgeInsets.only(left: 8, right: 8, top: 60),
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  env(EnvKey.PLATFORM_APP_NAME).toCapitalized(),
                  style: TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.w900,
                    fontFamily: FontFamily.stymie,
                    color: Colors.white,
                  ),
                ),
                IconButton(
                  onPressed: () {
                    AppRouter.pop();
                  },
                  icon: Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(
            height: MediaQuery.of(context).size.height * .033,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (_authStore.isAuthenticated)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          AppAvatar(
                            size: 20,
                            iconSize: 14,
                            backgroundColor: Colors.black,
                            photoURL: _authService.user?.getPhotoURL(),
                          ),
                          SizedBox(
                            height: 4,
                          ),
                          Text(
                            _authService.user?.displayName ?? "",
                            style: TextStyle(
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: MediaQuery.of(context).size.height * .05,
                      ),
                      Padding(
                        padding: const EdgeInsets.only(bottom: 20),
                        child: CoinsDisplay(
                          iconSize: 36,
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.w900,
                            fontFamily: FontFamily.urbanist,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                Observer(builder: (context) {
                  return Visibility(
                    visible: !mainStore.isPlayPass,
                    child: InkWell(
                      onTap: () {
                        AppRouter.pop();
                        _eventLogger.log(Events.shopClicked);
                        AppRouter.push(ShopView.routeName);
                      },
                      child: ListTile(
                        minLeadingWidth: 10,
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: 0,
                        ),
                        leading: SvgPicture.asset(
                          AppImages.storeIcon,
                        ),
                        title: Text(
                          "Store",
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            fontFamily: FontFamily.urbanist,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  );
                }),
                InkWell(
                  onTap: () {
                    AppRouter.pop();
                    _eventLogger.log(Events.shareAppClicked);
                    Loader.show(
                      context,
                      progressIndicator:
                          const CircularProgressIndicator.adaptive(),
                    );
                    createShareableImage(AppImages.featureImage)
                        .then((imagePath) {
                      SharePlus.instance
                          .share(
                            ShareParams(
                              files: [XFile(imagePath)],
                              text:
                                  'Check out ${env(EnvKey.PLAYSTORE_TITLE)} on the Google Play store ${env(EnvKey.PLAYSTORE_LINK)}',
                            ),
                          )
                          .then((_) => Loader.hide())
                          .catchError((_) => Loader.hide());
                    }).catchError((error) {
                      toast(context: context, msg: 'Unable to share.');
                      Loader.hide();
                    });
                  },
                  child: ListTile(
                    minLeadingWidth: 10,
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 0,
                    ),
                    leading: Icon(
                      Icons.share,
                      color: Colors.white,
                      size: 20,
                    ),
                    title: Text(
                      "Share",
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        fontFamily: FontFamily.urbanist,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
                InkWell(
                  onTap: () {
                    AppRouter.pop();
                    _eventLogger.log(Events.helpClicked);

                    showDialog(
                      context: context,
                      builder: (context) => HowToModal(),
                    );
                  },
                  child: ListTile(
                    minLeadingWidth: 10,
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 0,
                    ),
                    leading: Icon(
                      Icons.help_outline,
                      color: Colors.white,
                      size: 20,
                    ),
                    title: Text(
                      "Help",
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        fontFamily: FontFamily.urbanist,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
                InkWell(
                  onTap: () {
                    AppRouter.pop();
                    AppRouter.push(SettingsView.routeName);
                  },
                  child: ListTile(
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 0,
                    ),
                    minLeadingWidth: 10,
                    leading: Icon(
                      Icons.settings_rounded,
                      color: Colors.white,
                      size: 20,
                    ),
                    title: Text(
                      "Settings",
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        fontFamily: FontFamily.urbanist,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
