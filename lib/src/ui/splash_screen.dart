import 'dart:async';
import 'dart:isolate';

import 'package:cloud_firestore/cloud_firestore.dart';

import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:mobx/mobx.dart';
import 'package:wordle/src/core/constants/app_images.dart';
import 'package:wordle/src/core/constants/firestore_keys.dart';
import 'package:wordle/src/core/constants/user_prop.dart';
import 'package:wordle/src/core/helpers/users.dart';
import 'package:wordle/src/core/utils/env.dart';
import 'package:wordle/src/core/utils/local_storage.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/data/repositories/ironsource_ad_repository.dart';
import 'package:wordle/src/domain/auth/store/auth_store.dart';
import 'package:wordle/src/domain/main/stores/main_store.dart';
import 'package:wordle/src/domain/multiplayer/stores/multiplayer_store.dart';
import 'package:wordle/src/router/app_router.dart';
import 'package:wordle/src/services/auth/auth_service.dart';
import 'package:wordle/src/ui/theme/custom_theme.dart';
import 'package:wordle/src/ui/theme/custom_theme_text.dart';
import 'package:wordle/src/ui/views/entrance/entrance_view.dart';
import 'package:wordle/src/ui/widgets/folding_cube.dart';

import '../../main.dart';
import '../core/constants/events.dart';
import '../domain/shop/store/shop_store.dart';
import '../router/start_up_controller.dart';
import '../services/billing/billing_service.dart';
import '../services/events/event_logger_contract.dart';

class SplashView extends StatefulWidget {
  const SplashView({super.key});

  static const routeName = '/';

  @override
  State<SplashView> createState() => _SplashViewState();
}

class _SplashViewState extends State<SplashView> {
  final _authService = ServiceLocator.locate<AuthService>();
  final _mainStore = ServiceLocator.locate<MainStore>();
  final _authStore = ServiceLocator.locate<AuthStore>();
  final userMetaRef =
      FirebaseFirestore.instance.collection(FirestoreKeys.userMeta);
  final multiplayerStore = ServiceLocator.locate<MultiplayerStore>();
  final shopStore = ServiceLocator.locate<ShopStore>();
  final _billingService = ServiceLocator.locate<BillingService>();
  final _eventLogger = ServiceLocator.locate<EventLoggerContract>();

  final startup = StartUpController();
  late ReactionDisposer _disposer;

  @override
  void initState() {
    super.initState();
    _eventLogger.log(
      Events.splashVisited,
      params: AppStartupParam().param,
    );
    loadInit();
    _disposer =
        reaction((_) => shopStore.isSettingPlayPassCompleted, (bool completed) {
      if (completed) {
        AppRouter.pushAndRemoveUntil(
            EntranceView.routeName, (predicate) => false);
      }
    });
  }

  @override
  void dispose() {
    _disposer();
    super.dispose();
  }

  Future<void> loadInit() async {
    await loadHeavyFunctions();
    if (_authService.isAuthenticated()) {
      await startup.initializeApp();
      await loadStore();
      final userMeta = userMetaRef.doc(_authService.user!.uid).snapshots();
      userMeta.listen((snapshot) async {
        _mainStore.isSubscribed = IronSourceAdRepository.isSubscribed =
            snapshot.data()?['isSubscribed'] ?? false;
        setUserProp(
          key: UserProp.isSubscribed,
          value: (snapshot.data()?['isSubscribed'] ?? false).toString(),
        );
      });
    }
  }

  Future<void> loadStore() async {
    if (!kIsWeb) {
      shopStore.purchaseListStream = _billingService.purchaseStream.listen(
        shopStore.onPurchaseUpdate,
      );
    }
    await shopStore.loadStore().then(
          (value) => shopStore.restorePurchases(),
        );
  }

  Future<void> loadHeavyFunctions() async {
    await Future.wait(
      [
        callAds(),
        setUpConfigDefaults(),
        callFirebaseAuth(),
        callSubscribeToTopic(),
        callFetchAndActivateRemoteConfig(),
      ],
    );
  }

  Future<void> callFirebaseAuth() async {
    final authService = ServiceLocator.locate<AuthService>();
    final localStorage = ServiceLocator.locate<LocalStorage>();
    var user = authService.user;
    _authStore.isUserAnonymous = user?.isAnonymous ?? false;
    _authStore.user = user;
    if (authService.isAuthenticated() && !authService.isUserAnonymous) {
      localStorage.saveBool(LSKey.hasSignedInOnSocial, true);
    } else {
      bool isUserAnonymous = authService.isUserAnonymous;

      if (!isUserAnonymous) {
        await authService.signInAsGuest();
        localStorage.saveBool(LSKey.hasSignedInOnSocial, false);
        await _authStore.loadAuth();
      }
    }

    _eventLogger.log(
      Events.callFirebaseAuthCalled,
      params: AppStartupParam().param,
    );
  }

  Future<void> callSubscribeToTopic() async {
    FirebaseMessaging.instance.subscribeToTopic('worde');
    FirebaseMessaging.instance.subscribeToTopic('worde1.7.0');

    _eventLogger.log(
      Events.callSubscribeToTopicCalled,
      params: AppStartupParam().param,
    );
  }

  Future<void> callFetchAndActivateRemoteConfig() async {
    await FirebaseRemoteConfig.instance.fetchAndActivate();
    _eventLogger.log(
      Events.fetchActivateRemoteConfigCalled,
      params: AppStartupParam().param,
    );
  }

  Future<void> callAds() async {
    if (!kIsWeb) {
      MobileAds.instance.initialize();

      if (kDebugMode) {
        await FirebaseCrashlytics.instance
            .setCrashlyticsCollectionEnabled(false);
      } else {
        await FirebaseCrashlytics.instance
            .setCrashlyticsCollectionEnabled(true);
      }

      FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterError;

      Isolate.current.addErrorListener(RawReceivePort((pair) async {
        final List<dynamic> errorAndStacktrace = pair;
        await FirebaseCrashlytics.instance.recordError(
          errorAndStacktrace.first,
          errorAndStacktrace.last,
        );
      }).sendPort);
    } else {
      // initialiaze the facebook javascript SDK
      FacebookAuth.i.webAndDesktopInitialize(
        appId: env(EnvKey.FACEBOOK_APP_ID),
        cookie: true,
        xfbml: true,
        version: "v13.0",
      );
    }
    _eventLogger.log(
      Events.callAdsCalled,
      params: AppStartupParam().param,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          CustomTheme.isLightMode(context) ? Colors.white : Colors.black,
      body: SizedBox(
        width: double.infinity,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Spacer(
              flex: 4,
            ),
            SvgPicture.asset(
              AppImages.logoGridThin,
              height: 128,
              width: 128,
            ),
            SizedBox(
              height: 14,
            ),
            Text(
              env(EnvKey.PLATFORM_APP_NAME).toCapitalized(),
              style: CustomThemeText.stymieTextBlack(
                fontSize: 40,
                context: context,
                fontWeight: FontWeight.w900,
              ),
            ),
            SizedBox(
              height: MediaQuery.of(context).size.height * .13,
            ),
            FoldingCube(
              color: CustomTheme.getBlackIconColor(context),
              size: 24,
            ),
            Spacer(
              flex: 5,
            ),
          ],
        ),
      ),
    );
  }
}
